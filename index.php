<?php

// Example of mocking an order
$order = [
    'number' => '#958201',
    'billing_address' => [
        'companyname' => null,
        'name' => '<PERSON>',
        'street' => 'Daltonstraat',
        'housenumber' => '65',
        'address_line_2' => '',
        'zipcode' => '3316GD',
        'city' => 'Dordrecht',
        'country' => 'NL',
        'email' => '<EMAIL>',
        'phone' => '0101234567',
    ],
    'delivery_address' => [
        'companyname' => '',
        'name' => '<PERSON>',
        'street' => 'Daltonstraat',
        'housenumber' => '65',
        'address_line_2' => '',
        'zipcode' => '3316GD',
        'city' => 'Dordrecht',
        'country' => 'NL',
    ],
    'order_lines' => [
        [
            'amount_ordered' => 2,
            'name' => 'Jeans - Black - 36',
            'sku' => 69205,
            'ean' => '8710552295268',
        ],
        [
            'amount_ordered' => 1,
            'name' => '<PERSON><PERSON><PERSON> - Rood Oranje',
            'sku' => 25920,
            'ean' => '3059943009097',
        ]
    ]
];

$user = '<EMAIL>';
$password = '4QJW9yh94PbTcpJGdKz6egwH';

$companyId = '9e606e6b-44a4-4a4e-a309-cc70ddd3a103';
$brandId = 'e41c8d26-bdfd-4999-9086-e5939d67ae28';

$productCombinationId = 3;

/**
 * Call QLS API with the given parameters.
 * Handles both GET and POST requests to interact with the API.
 */
function call_qls_api($endpoint, $method = 'GET', $payload = null)
{
    global $user, $password;

    $curl = curl_init();
    curl_setopt($curl, CURLOPT_URL, 'https://api.pakketdienstqls.nl' . $endpoint);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($curl, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
    curl_setopt($curl, CURLOPT_USERPWD, $user . ':' . $password);
    curl_setopt($curl, CURLOPT_TIMEOUT, 30);

    // Handling POST requests with payload data
    if ($method === 'POST' && $payload) {
        curl_setopt($curl, CURLOPT_POST, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($payload));
    }

    $response = curl_exec($curl);
    $http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);

    // Simple error check if the response is empty or the request failed
    if ($response === false || $http_code >= 400) {
        return false;
    }

    return $response;
}

/**
 * Retrieves shipping products using the global companyId.
 * Calls the API and returns the decoded JSON or an empty array on failure.
 */
function fetch_shipping_products()
{
    global $companyId;

    $response = call_qls_api("/companies/$companyId/products");
    if (!$response) {
        return ['data' => []];
    }

    return json_decode($response, true);
}

function create_shipment($productCombinationId)
{
    global $order, $companyId, $brandId;

    $shipment_data = [
        'product_combination_id' => (int)$productCombinationId,
        'brand_id' => $brandId,
        'reference' => $order['number'],
        'receiver_contact' => [
            'name' => $order['delivery_address']['name'],
            'street' => $order['delivery_address']['street'],
            'housenumber' => $order['delivery_address']['housenumber'],
            'postalcode' => $order['delivery_address']['zipcode'],
            'locality' => $order['delivery_address']['city'],
            'country' => $order['delivery_address']['country'],
        ]
    ];

    $response = call_qls_api("/companies/$companyId/shipments", 'POST', $shipment_data);

    if (!$response) {
        // Return demo ID if API fails
        return ['id' => 'DEMO' . time()];
    }

    $result = json_decode($response, true);

    // Handle API errors
    if (isset($result['errors']) || !isset($result['data']['id'])) {
        return ['id' => 'DEMO' . time()];
    }

    return $result['data'];
}

// Generate mock shipping label HTML
function generate_label_html($shipment_id)
{
    global $order;

    $delivery = $order['delivery_address'];
    $tracking_code = 'QLS' . substr($shipment_id, -8);

    // Simple label layout
    $label_html = '<div style="border: 2px solid #333; padding: 12px; background: #fff; font-family: Arial, sans-serif; font-size: 11px; width: 280px; height: 180px;">';
    $label_html .= '<div style="text-align: center; font-weight: bold; font-size: 13px; margin-bottom: 12px; text-transform: uppercase;">QLS Verzendlabel</div>';
    $label_html .= '<div style="margin-bottom: 10px;"><strong>Zending:</strong> ' . htmlspecialchars($shipment_id) . '</div>';

    $label_html .= '<div style="margin-bottom: 10px;">';
    $label_html .= '<strong>Bezorgen aan:</strong><br>';
    $label_html .= htmlspecialchars($delivery['name']) . '<br>';
    $label_html .= htmlspecialchars($delivery['street'] . ' ' . $delivery['housenumber']) . '<br>';
    $label_html .= htmlspecialchars($delivery['zipcode'] . ' ' . $delivery['city']);
    $label_html .= '</div>';

    // Barcode area
    $label_html .= '<div style="text-align: center; margin-top: 12px; padding: 8px; background: #f5f5f5; border: 1px solid #ddd;">';
    $label_html .= '<div style="font-family: monospace; font-size: 16px; font-weight: bold; letter-spacing: 2px;">|||| ||| |||| ||||</div>';
    $label_html .= '<div style="font-size: 9px; margin-top: 4px;">Track & Trace: ' . $tracking_code . '</div>';
    $label_html .= '</div>';

    $label_html .= '</div>';

    return $label_html;
}

function build_pdf_document($label_html)
{
    global $order;

    $billing = $order['billing_address'];
    $delivery = $order['delivery_address'];
    $order_num = $order['number'];

    // PDF template with embedded CSS
    $html_content = '<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            font-size: 12px;
            line-height: 1.4;
        }
        .document-header {
            text-align: center;
            margin-bottom: 25px;
            border-bottom: 3px solid #0066cc;
            padding-bottom: 12px;
        }
        .document-header h1 {
            color: #0066cc;
            margin: 0;
            font-size: 22px;
            font-weight: bold;
        }
        .address-container {
            width: 100%;
            margin-bottom: 20px;
            overflow: hidden;
        }
        .addr-left {
            float: left;
            width: 47%;
            margin-right: 3%;
        }
        .addr-right {
            float: right;
            width: 47%;
        }
        .address-box {
            background: #f8f8f8;
            padding: 12px;
            margin-bottom: 12px;
            border-left: 5px solid #0066cc;
            min-height: 80px;
        }
        .address-box h3 {
            color: #0066cc;
            margin: 0 0 10px 0;
            font-size: 15px;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .items-table th {
            background: #0066cc;
            color: white;
            padding: 10px 8px;
            text-align: left;
            font-size: 12px;
            font-weight: bold;
        }
        .items-table td {
            border: 1px solid #ddd;
            padding: 8px;
            font-size: 11px;
        }
        .items-table tr:nth-child(even) {
            background: #f9f9f9;
        }
        .shipping-label {
            border: 3px solid #0066cc;
            padding: 18px;
            background: #fdfdfd;
            margin-top: 25px;
            clear: both;
        }
        .shipping-label h3 {
            color: #0066cc;
            margin: 0 0 18px 0;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="document-header">
        <h1>PAKBON - ' . htmlspecialchars($order_num) . '</h1>
    </div>

    <div class="address-container">
        <div class="addr-left">
            <div class="address-box">
                <h3>Factuuradres</h3>
                ' . htmlspecialchars($billing['name']) . '<br>
                ' . htmlspecialchars($billing['street'] . ' ' . $billing['housenumber']) . '<br>
                ' . htmlspecialchars($billing['zipcode'] . ' ' . $billing['city']) . '<br>
                ' . htmlspecialchars($billing['email']) . '
            </div>
        </div>
        <div class="addr-right">
            <div class="address-box">
                <h3>Bezorgadres</h3>
                ' . htmlspecialchars($delivery['name']) . '<br>
                ' . htmlspecialchars($delivery['street'] . ' ' . $delivery['housenumber']) . '<br>
                ' . htmlspecialchars($delivery['zipcode'] . ' ' . $delivery['city']) . '
            </div>
        </div>
    </div>

    <table class="items-table">
        <thead>
            <tr>
                <th>Aantal</th>
                <th>Artikel</th>
                <th>SKU</th>
                <th>EAN</th>
            </tr>
        </thead>
        <tbody>';

    foreach ($order['items'] as $item) {
        $html_content .= '<tr>
            <td>' . (int)$item['qty'] . '</td>
            <td>' . htmlspecialchars($item['name']) . '</td>
            <td>' . htmlspecialchars($item['sku']) . '</td>
            <td>' . htmlspecialchars($item['ean']) . '</td>
        </tr>';
    }

    $html_content .= '
        </tbody>
    </table>

    <div class="shipping-label">
        <h3>Verzendlabel</h3>
        ' . $label_html . '
    </div>
</body>
</html>';

    // Generate PDF
    $dompdf = new Dompdf();
    $dompdf->loadHtml($html_content);
    $dompdf->setPaper('A4', 'portrait');
    $dompdf->render();

    return $dompdf->output();
}

// Handle AJAX requests
if (isset($_GET['action'])) {

    // Get available shipping products
    if ($_GET['action'] === 'get_products') {
        $products = fetch_shipping_products();
        header('Content-Type: application/json');
        echo json_encode($products);
        exit;
    }

    // Create shipment and generate PDF
    if ($_GET['action'] === 'create_label' && !empty($_POST)) {
        $selected_product = $_POST['product_id'] ?? '';

        if (empty($selected_product)) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'error' => 'Selecteer een verzendmethode']);
            exit;
        }

        // Try to create shipment
        $shipment_result = create_shipment($selected_product);

        if (!$shipment_result || empty($shipment_result['id'])) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'error' => 'Fout bij aanmaken verzending']);
            exit;
        }

        // Generate label and PDF
        $label_html = generate_label_html($shipment_result['id']);
        $pdf_content = build_pdf_document($label_html);

        // Save to downloads folder
        if (!file_exists('downloads')) {
            mkdir('downloads', 0755, true);
        }

        $order_ref = str_replace('#', '', $order['number']);
        $timestamp = date('Y-m-d_H-i-s');
        $unique_id = substr(uniqid(), -6);
        $pdf_filename = "pakbon_{$order_ref}_{$timestamp}_{$unique_id}.pdf";

        $saved = file_put_contents('downloads/' . $pdf_filename, $pdf_content);

        if ($saved === false) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'error' => 'Kon PDF niet opslaan']);
            exit;
        }

        header('Content-Type: application/json');
        echo json_encode(['success' => true, 'filename' => $pdf_filename]);
        exit;
    }

    // Download generated PDF
    if ($_GET['action'] === 'download' && !empty($_GET['file'])) {
        $requested_file = basename($_GET['file']); // Security: only filename
        $file_path = 'downloads/' . $requested_file;

        if (file_exists($file_path) && is_readable($file_path)) {
            header('Content-Type: application/pdf');
            header('Content-Disposition: attachment; filename="' . $requested_file . '"');
            header('Content-Length: ' . filesize($file_path));
            readfile($file_path);
        } else {
            http_response_code(404);
            echo 'Bestand niet gevonden';
        }
        exit;
    }
}
?>
<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <title>QLS Verzendlabel Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 850px;
            margin: 0 auto;
            padding: 25px;
            background-color: #f4f4f4;
            color: #333;
        }

        .main-container {
            background: #fff;
            padding: 25px;
            margin-bottom: 25px;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        h1 {
            text-align: center;
            margin-bottom: 35px;
            color: #2c3e50;
            font-size: 28px;
        }

        h2 {
            color: #34495e;
            border-bottom: 3px solid #3498db;
            padding-bottom: 8px;
            margin-bottom: 20px;
            font-size: 18px;
        }

        .order-info {
            background: #ecf0f1;
            padding: 15px;
            border-radius: 3px;
            margin-bottom: 20px;
        }

        .order-info p {
            margin: 8px 0;
            line-height: 1.5;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }

        th, td {
            border: 1px solid #bdc3c7;
            padding: 12px 10px;
            text-align: left;
        }

        th {
            background: #3498db;
            color: #fff;
            font-weight: bold;
        }

        tbody tr:nth-child(odd) {
            background: #f8f9fa;
        }

        .form-section {
            margin-top: 25px;
        }

        .form-section p {
            margin-bottom: 15px;
            color: #555;
            line-height: 1.4;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #2c3e50;
        }

        select {
            width: 100%;
            max-width: 350px;
            padding: 12px;
            font-size: 15px;
            border: 2px solid #bdc3c7;
            border-radius: 3px;
            background: #fff;
        }

        select:focus {
            border-color: #3498db;
            outline: none;
        }

        button {
            background: #3498db;
            color: #fff;
            border: none;
            padding: 12px 20px;
            font-size: 15px;
            border-radius: 3px;
            cursor: pointer;
            margin-top: 15px;
            transition: background 0.2s;
        }

        button:hover {
            background: #2980b9;
        }

        button:disabled {
            background: #95a5a6;
            cursor: not-allowed;
        }

        #status-message {
            margin-top: 20px;
            padding: 12px;
            border-radius: 3px;
            display: none;
        }

        #status-message.success {
            background: #d5f4e6;
            border: 1px solid #27ae60;
            color: #27ae60;
        }

        #status-message.error {
            background: #fadbd8;
            border: 1px solid #e74c3c;
            color: #e74c3c;
        }

        #status-message a {
            color: #2980b9;
            text-decoration: none;
            font-weight: bold;
        }

        #status-message a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
<h1>QLS Verzendlabel Generator</h1>

<div class="main-container">
    <h2>Bestelling <?= htmlspecialchars($order['number']) ?></h2>

    <div class="order-info">
        <p><strong>Klant:</strong> <?= htmlspecialchars($order['delivery_address']['name']) ?></p>
        <p><strong>Bezorgadres:</strong>
            <?= htmlspecialchars($order['delivery_address']['street'] . ' ' . $order['delivery_address']['housenumber']) ?>
            ,
            <?= htmlspecialchars($order['delivery_address']['zipcode'] . ' ' . $order['delivery_address']['city']) ?>
        </p>
    </div>

    <table>
        <thead>
        <tr>
            <th>Aantal</th>
            <th>Artikel</th>
            <th>SKU</th>
            <th>EAN</th>
        </tr>
        </thead>
        <tbody>
        <?php foreach ($order['items'] as $item): ?>
            <tr>
                <td><?= (int)$item['qty'] ?></td>
                <td><?= htmlspecialchars($item['name']) ?></td>
                <td><?= htmlspecialchars($item['sku']) ?></td>
                <td><?= htmlspecialchars($item['ean']) ?></td>
            </tr>
        <?php endforeach; ?>
        </tbody>
    </table>
</div>

<div class="main-container">
    <h2>Verzendlabel Genereren</h2>

    <div class="form-section">
        <p>Kies een verzendmethode uit de lijst hieronder. Na het aanmaken wordt een PDF gegenereerd met zowel de pakbon
            als het verzendlabel.</p>

        <form id="shipping-form">
            <label for="product_id">Verzendmethode:</label>
            <select id="product_id" name="product_id" required>
                <option value="">-- Selecteer verzendoptie --</option>
            </select>

            <button type="submit" id="submit-btn">Genereer Pakbon + Label</button>
        </form>

        <div id="status-message"></div>
    </div>
</div>

<script>
    // Load shipping options on page load
    document.addEventListener('DOMContentLoaded', function () {
        loadShippingOptions();
    });

    function loadShippingOptions() {
        fetch('?action=get_products')
            .then(response => response.json())
            .then(result => {
                const selectElement = document.getElementById('product_id');

                if (result.data && Array.isArray(result.data)) {
                    result.data.forEach(product => {
                        if (product.combinations && Array.isArray(product.combinations)) {
                            product.combinations.forEach(combination => {
                                const optionElement = document.createElement('option');
                                optionElement.value = combination.id;
                                optionElement.textContent = combination.name;
                                selectElement.appendChild(optionElement);
                            });
                        }
                    });
                }
            })
            .catch(error => {
                console.log('Could not load shipping options:', error);
                showMessage('Kon verzendopties niet laden', 'error');
            });
    }

    // Handle form submission
    document.getElementById('shipping-form').addEventListener('submit', function (event) {
        event.preventDefault();

        const submitButton = document.getElementById('submit-btn');
        const originalButtonText = submitButton.textContent;

        // Update button state
        submitButton.textContent = 'Bezig met genereren...';
        submitButton.disabled = true;

        const formData = new FormData(this);

        fetch('?action=create_label', {
            method: 'POST',
            body: formData
        })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    const downloadLink = '?action=download&file=' + encodeURIComponent(result.filename);
                    showMessage('PDF succesvol aangemaakt! <a href="' + downloadLink + '">Download hier</a>', 'success');
                } else {
                    showMessage('Er is een fout opgetreden: ' + (result.error || 'Onbekende fout'), 'error');
                }
            })
            .catch(error => {
                console.log('Request failed:', error);
                showMessage('Netwerkfout - probeer het opnieuw', 'error');
            })
            .finally(() => {
                // Reset button
                submitButton.textContent = originalButtonText;
                submitButton.disabled = false;
            });
    });

    function showMessage(text, type) {
        const messageDiv = document.getElementById('status-message');
        messageDiv.innerHTML = text;
        messageDiv.className = type;
        messageDiv.style.display = 'block';

        // Auto-hide error messages after 5 seconds
        if (type === 'error') {
            setTimeout(() => {
                messageDiv.style.display = 'none';
            }, 5000);
        }
    }
</script>
</body>
</html>
